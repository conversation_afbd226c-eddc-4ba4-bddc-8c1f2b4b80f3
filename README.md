# Options backend

Ce backend est propulsé par [Firebase Cloud Functions](https://firebase.google.com/docs/functions) et [Express](https://expressjs.com/).

## Authentification

L'authentification est effectuée via une clé d'API. Les endpoints sont protégés par un middleware qui vérifie que la clé d'API est présente et valide (voir [api-key.js](./functions/middlewares/api-key.js)).

Les clés d'API autorisées sont stockées dans une base de donnée Firebase Realtime Database dans la collection `apiKeys`. Chaque élément du tableau contient une clé d'API formatée comme suit :

```json
{
  "name": "string",
  "value": "string",
  "permissions": "string"
}
```

Une clé est une chaîne de caractères alphanumérique de 128 bits. Vous pouvez générer une clé en utilisant le site suivant : [Api Key Generator](https://generate-random.org/api-key-generator?count=1&length=128&type=mixed-numbers&prefix=key_).

##### name

Nom de la clé.

##### value

Chaîne de caractères alphanumérique de 128 bits. C'est cette valeur que vous devez renseigner dans le header `x-api-key`.

##### permissions

Chaîne de caractères séparée par un point-virgule `;` détaillant les permissions liées à cette clé. Les permissions sont évoquées sous cette forme : `create/configuration;create/configuration-id`.

## Endpoints

Tous les endpoints sont préfixés par `/api`. L'api est versionnée et la version actuelle est `v1`. Chaque endpoint est préfixé par `/v1`.

Toutes les requêtes sont authentifiées grace à une clé d'API. Veillez à inclure la clé d'API dans le header `x-api-key` de vos requêtes.

---

### `GET /api/v1/config-id`

Crée un nouvel identifiant de configuration en base de donnée. La réponse contient l'identifiant de configuration et l'url du configurateur avec l'identifiant de configuration en paramètre si la création est réussie.

#### Réponses

##### `201 Created`

```json
{
  "id": "string",
  "url": "string"
}
```

##### `401 Unauthorized`

Clé d'API manquante dans le header `x-api-key`

```json
{
  "message": "string",
  "detail": "string"
}
```

##### `500 Internal Server Error`

```json
{
  "message": "string",
  "detail": "string"
}
```

### `GET /api/v1/configs/:id`

Récupère le détail d'une configuration en base de donnée avec l'`id` renseigné. L'url temporaire (expiration 5min) d'accès au modèle est également ajouté dans la réponse dans la propriété `url`.

#### Réponses

##### `200 Success`

```json
{
  "skus": "string[]",
  "quotationSkus": "string[]",
  "name": "string",
  "uploaded": "boolean",
  "id": "string",
  "url": "string"
}
```

##### `404 Not Found`

Aucune configuration n'a été trouvée avec l'`id` renseigné.

```json
{
  "message": "Not Found",
  "detail": "Configuration not found"
}
```

### `POST /api/v1/configs`

Permet d'ajouter une configuration en base de données. La réponse contient l'identifiant unique de la configuration créée, ainsi que son détail.

#### Body

```json
{
  "name": "string - optional",
  "skus": "string[] - required",
  "quotationSkus": "string[]",
  "sessionId": "uuidv4 - required"
}
```

#### Réponses

##### `201 Created`

```json
{
  "id": "string",
  "name": "string",
  "skus": "string[]",
  "quotationSkus": "string[]",
  "sessionId": "uuidv4",
  "uploadUrl": "uri"
}
```

##### `400 Bad Request`

Propriété manquant dans le corps de la requête. `detail` vous précisera quelle partie du corps de votre requête est manquant.

```json
{
  "message": "Bad Request",
  "detail": "<dynamic>"
}
```

##### `401 Unauthorized`

Clé d'API manquante.

```json
{
  "message": "string",
  "detail": "string"
}
```

##### `403 Unauthorized`

La clé d'API que vous utilisez ne possède pas les droits nécessaires.

```json
{
  "message": "string",
  "detail": "string"
}
```

### `POST /api/v1/configs/:id`

Permet de mettre à jour une configuration en base de données. La réponse contient l'identifiant unique de la configuration créée, ainsi que son détail.

#### Body

```json
{
  "name": "string - optional",
  "skus": "string[] - required",
  "quotationSkus": "string[] - optional",
  "sessionId": "uuidv4 - required"
}
```

#### Réponses

##### `200 Success`

```json
{
  "id": "string",
  "name": "string",
  "skus": "string[]",
  "quotationSkus": "string[]",
  "sessionId": "uuidv4",
  "uploadUrl": "uri"
}
```

##### `400 Bad Request`

```json
{
  "message": "Bad Request",
  "detail": "<dynamic>"
}
```

##### `401 Unauthorized`

```json
{
  "message": "string",
  "detail": "string"
}
```

##### `403 Unauthorized`

La clé d'API que vous utilisez ne possède pas les droits nécessaires.

```json
{
  "message": "string",
  "detail": "string"
}
```

##### `403 Forbidden`

L'identifiant de session renseigné n'est pas autorisé à modifier la configuration.

```json
{
  "message": "string",
  "detail": "string"
}
```

##### `404 Not Found`

Aucune configuration n'a été trouvée avec l'`id` renseigné.

```json
{
  "message": "Not Found",
  "detail": "Configuration not found"
}
```

### `GET /api/v1/products/:id/exists`

Vérifie l'existence d'un produit dans la base de données Notion en fonction de son SKU. L'endpoint interroge la base de données Notion avec des filtres sur la propriété `usage` (multi-select) pour la valeur "Configurateur" et la propriété `sku` (rich text) correspondant au paramètre de route.

#### Paramètres

##### `:id` (string, required)

Le SKU du produit à vérifier. Doit être une chaîne de caractères composée uniquement de chiffres.

#### Exemple de requête

```bash
GET /api/v1/products/123456/exists
```

#### Réponses

##### `200 Success`

Le produit existe dans la base de données Notion.

```json
{
  "id": "123456",
  "exists": true
}
```

##### `400 Bad Request`

Le paramètre SKU fourni n'est pas valide (doit contenir uniquement des chiffres).

```json
{
  "message": "Bad Request",
  "detail": "\"id\" with value \"abc123\" fails to match the required pattern: /^\\d+$/"
}
```

##### `401 Unauthorized`

Clé d'API manquante dans le header `x-api-key`.

```json
{
  "message": "string",
  "detail": "string"
}
```

##### `403 Forbidden`

La clé d'API que vous utilisez ne possède pas les droits nécessaires (`read/product`).

```json
{
  "message": "string",
  "detail": "string"
}
```

##### `404 Not Found`

Le produit n'existe pas dans la base de données Notion ou ne correspond pas aux critères de filtrage.

```json
{
  "id": "123456",
  "exists": false
}
```

##### `500 Internal Server Error`

Erreur interne du serveur, généralement liée à la configuration Notion ou à un problème de connexion.

```json
{
  "message": "Internal Server Error"
}
```

#### Notes techniques

- **Intégration Notion** : L'endpoint utilise l'API Notion pour interroger une base de données configurée via les variables d'environnement `NOTION_TOKEN` et `NOTION_DATABASE_ID`.
- **Filtres appliqués** :
  - Propriété `usage` (multi-select) : doit contenir "Configurateur"
  - Propriété `sku` (rich text) : doit être égale au paramètre `:id`
- **Permissions requises** : La clé d'API doit avoir la permission `read/product`.
- **Validation** : Le SKU doit être composé uniquement de chiffres (regex: `/^\d+$/`).

## Base de donnée des configurations

La base de donnée des configuration est une base de donnée [Firestore](https://firebase.google.com/docs/firestore). Elle contient une collection `configs` qui contient les configurations des utilisateurs. L'id de chaque document est l'id de configuration.

## Storage

### Hook `onObjectFinalize`

Un hook est configuré pour déclencher une fonction lorsqu'un objet est finalisé dans le bucket de stockage. L'entrée en base de donnée est alors mise à jour pour indiquer que l'objet a été uploadé (champs `uploaded`).

## Cron jobs

### Nettoyage des configurations de test

Un cron job est configuré pour nettoyer les configurations de test. Les configurations de test sont identifiées quand le champ `value` est égal à `test`. Les configurations de test sont supprimées tous les jours à minuit.

Vous pouvez forcer l'exécution du cron job depuis la [console Google Cloud](https://console.cloud.google.com/cloudscheduler).

> Voir [delete-test-configs.js](./functions/crons/delete-test-configs.js).
