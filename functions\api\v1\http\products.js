const logger = require("firebase-functions/logger");
const express = require("express");
const Joi = require("joi");
const { Client } = require("@notionhq/client");
const permit = require("../../../middlewares/authorization");

const router = express.Router();

const validationSchema = Joi.object({
  id: Joi.string().regex(/^\d+$/).required(),
});

router.get("/products/:id/exists", permit("read/product"), async (req, res) => {
  const { error, value } = validationSchema.validate(req.params);

  if (error) {
    logger.warn("Invalid SKU", { error });

    return res.status(400).json({
      message: "Bad Request",
      detail: error.message,
    });
  }

  const sku = value.id;

  try {
    // eslint-disable-next-line no-undef
    const notionToken = process.env.NOTION_TOKEN;
    // eslint-disable-next-line no-undef
    const notionDatabaseId = process.env.NOTION_DATABASE_ID;

    if (!notionToken) {
      logger.error("NOTION_TOKEN secret is not available");

      return res.status(500).json({
        message: "Internal Server Error",
      });
    }

    if (!notionDatabaseId) {
      logger.error("NOTION_DATABASE_ID secret is not available");

      return res.status(500).json({
        message: "Internal Server Error",
      });
    }

    const notion = new Client({ auth: notionToken });

    // Create database query with filters
    const databaseQuery = {
      database_id: notionDatabaseId,
      filter: {
        and: [
          {
            property: "usage",
            multi_select: {
              contains: "Configurateur",
            },
          },
          {
            property: "sku",
            rich_text: {
              equals: sku,
            },
          },
        ],
      },
    };

    logger.info("Executing Notion database query", databaseQuery);

    const response = await notion.databases.query(databaseQuery);

    logger.info("Notion database query response", {
      resultsCount: response.results.length,
      query: databaseQuery,
    });

    if (response.results.length === 0) {
      return res.status(404).json({
        id: sku,
        exists: false,
      });
    }

    res.status(200).json({
      id: sku,
      exists: true,
    });
  } catch (error) {
    logger.error("Error in products exists endpoint", {
      error: error.message,
      stack: error.stack,
      id: sku,
    });

    res.status(500).json({
      message: "Internal Server Error",
    });
  }
});

module.exports = router;
